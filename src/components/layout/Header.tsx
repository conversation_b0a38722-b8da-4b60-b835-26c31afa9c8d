"use client"; // Add this for client-side interactivity (mobile menu toggle)

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation'; // Import usePathname
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline'; // Example icons
import { useAuth } from "@/contexts/AuthContext"; // Assuming useAuth is exported from AuthContext.tsx

// Define NavLink and ActionLink types here
export interface NavLink {
  name: string;
  href: string;
  type: "link";
  disabled?: boolean;
}

export interface ActionButton {
  name: string;
  onClick: () => void;
  type: "button";
  disabled?: boolean;
}

export type ActionLink = NavLink | ActionButton;

const navLinks = [
  { href: '/', label: 'Home' },
  { href: '/browse', label: 'Browse' },
  { href: '/submit', label: 'Submit New' },
  { href: '/login', label: 'Login' },
  { href: '/register', label: 'Register' },
];

export default function Header() {
  const { user, session, isLoading, authError, logout } = useAuth(); // Get logout from context
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();

  // Log auth state whenever Header renders
  console.log('[Header] Rendering. Auth state - User:', user, 'Session:', session, 'IsLoading:', isLoading, 'Error:', authError); // DEBUG

  const handleSignOut = async () => {
    console.log('[Header] handleSignOut called');
    // No need to create supabase client here, logout from context handles it.
    await logout();
    // router.push('/'); // AuthContext.logout can handle redirection if desired
    // No need to manually call router.refresh() if AuthContext handles state updates properly
  };

  const baseNavLinks: NavLink[] = [
    { name: "Home", href: "/", type: "link" },
    { name: "Browse", href: "/browse", type: "link" },
    { name: "Submit Resource", href: "/submit", type: "link" },
  ];

  let dynamicActionLinks: ActionLink[] = [];
  if (isLoading) {
    console.log('[Header] Auth state is loading...'); // DEBUG
    dynamicActionLinks = [{ name: "Loading...", href: "#", type: "link", disabled: true }];
  } else if (user && session) {
    console.log('[Header] User is logged in. Email:', user.email); // DEBUG
    dynamicActionLinks = [
      { name: `User: ${user.email ? user.email.split('@')[0] : 'Profile'}`, href: "/profile", type: "link" },
      { name: "Logout", onClick: handleSignOut, type: "button" },
    ];
  } else {
    console.log('[Header] User is not logged in.'); // DEBUG
    dynamicActionLinks = [
      { name: "Login", href: "/login", type: "link" },
      { name: "Register", href: "/register", type: "link" },
    ];
  }

  const allNavLinks = [...baseNavLinks, ...dynamicActionLinks];
  const allMobileNavLinks = [...baseNavLinks, ...dynamicActionLinks.filter(link => link.type === 'link' || link.type === 'button')]; // Mobile can have buttons too

  return (
    <header className="bg-gray-800 text-white p-4 shadow-md">
      <div className="container mx-auto flex justify-between items-center">
        <Link href="/" className="text-2xl font-bold hover:text-gray-300 transition-colors">
          AI Navigator
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex space-x-4">
          {baseNavLinks.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                pathname === item.href
                  ? "bg-gray-900 text-white"
                  : "text-gray-300 hover:bg-gray-700 hover:text-white"
              }`}
            >
              {item.name}
            </Link>
          ))}
          {dynamicActionLinks.map((item) =>
            item.type === "link" ? (
              <Link
                key={item.name}
                href={item.href}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  pathname === item.href
                    ? "bg-gray-900 text-white"
                    : "text-gray-300 hover:bg-gray-700 hover:text-white"
                } ${item.disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                aria-disabled={item.disabled}
                onClick={(e) => item.disabled && e.preventDefault()}
              >
                {item.name}
              </Link>
            ) : (
              <button
                key={item.name}
                onClick={item.onClick}
                disabled={item.disabled}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors text-gray-300 hover:bg-gray-700 hover:text-white ${item.disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {item.name}
              </button>
            )
          )}
        </nav>

        {/* Mobile Menu Button */}
        <div className="md:hidden">
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
          >
            <span className="sr-only">Open main menu</span>
            {isMobileMenuOpen ? (
              <XMarkIcon className="block h-6 w-6" aria-hidden="true" />
            ) : (
              <Bars3Icon className="block h-6 w-6" aria-hidden="true" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            {allMobileNavLinks.map((item) =>
              item.type === "link" ? (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={() => setIsMobileMenuOpen(false)} 
                  className={`block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                    pathname === item.href
                      ? "bg-gray-900 text-white"
                      : "text-gray-300 hover:bg-gray-700 hover:text-white"
                  } ${item.disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                  aria-disabled={item.disabled}
                >
                  {item.name}
                </Link>
              ) : (
                <button
                  key={item.name}
                  onClick={() => { item.onClick(); setIsMobileMenuOpen(false); }}
                  disabled={item.disabled}
                  className={`block w-full text-left px-3 py-2 rounded-md text-base font-medium transition-colors text-gray-300 hover:bg-gray-700 hover:text-white ${item.disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  {item.name}
                </button>
              )
            )}
          </div>
        </div>
      )}
    </header>
  );
} 