import React from 'react';

const ResourceCardSkeleton: React.FC = () => {
  return (
    <div className="bg-card border border-border rounded-lg overflow-hidden flex flex-col h-full animate-pulse">
      {/* Image Section Skeleton */}
      <div className="relative w-full h-48 bg-muted/50">
        <div className="absolute inset-4 bg-muted rounded skeleton"></div>
      </div>

      {/* Content Section Skeleton */}
      <div className="p-6 flex flex-col flex-grow space-y-4">
        {/* Title Skeleton */}
        <div className="space-y-2">
          <div className="h-5 bg-muted rounded skeleton w-3/4"></div>
          <div className="h-5 bg-muted rounded skeleton w-1/2"></div>
        </div>

        {/* Badges Skeleton */}
        <div className="flex gap-2">
          <div className="h-6 bg-muted rounded-full skeleton w-16"></div>
          <div className="h-6 bg-muted rounded-full skeleton w-20"></div>
        </div>

        {/* Description Skeleton */}
        <div className="space-y-2 flex-grow">
          <div className="h-4 bg-muted rounded skeleton w-full"></div>
          <div className="h-4 bg-muted rounded skeleton w-full"></div>
          <div className="h-4 bg-muted rounded skeleton w-2/3"></div>
        </div>

        {/* Footer Section Skeleton */}
        <div className="mt-auto space-y-4">
          {/* Rating Skeleton */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="h-4 w-4 bg-muted rounded skeleton"></div>
              <div className="h-4 bg-muted rounded skeleton w-12"></div>
              <div className="h-4 bg-muted rounded skeleton w-16"></div>
            </div>
            <div className="h-4 w-4 bg-muted rounded skeleton"></div>
          </div>

          {/* Tags Skeleton */}
          <div className="flex gap-1">
            <div className="h-6 bg-muted rounded skeleton w-12"></div>
            <div className="h-6 bg-muted rounded skeleton w-16"></div>
            <div className="h-6 bg-muted rounded skeleton w-14"></div>
          </div>

          {/* CTA Button Skeleton */}
          <div className="h-10 bg-muted rounded skeleton w-full"></div>
        </div>
      </div>
    </div>
  );
};

export default ResourceCardSkeleton;
