import React from 'react';

const ResourceCardSkeleton: React.FC = () => {
  return (
    <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden animate-pulse">
      <div className="p-5">
        <div className="flex items-start space-x-4">
          <div className="w-16 h-16 bg-gray-300 dark:bg-gray-700 rounded-md"></div>
          <div className="flex-1 space-y-3 py-1">
            <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-3/4"></div>
            <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-1/2"></div>
            <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-5/6"></div>
          </div>
        </div>
        <div className="mt-4 space-y-2">
          <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-full"></div>
          <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-full"></div>
          <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-2/3"></div>
        </div>
        <div className="mt-4 flex justify-between items-center">
          <div className="flex items-center space-x-1">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="w-4 h-4 bg-gray-300 dark:bg-gray-700 rounded-full"></div>
            ))}
            <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-10 ml-1"></div>
          </div>
          <div className="h-6 bg-gray-300 dark:bg-gray-700 rounded w-20"></div> 
        </div>
        <div className="mt-3 flex flex-wrap gap-2">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-4 bg-gray-300 dark:bg-gray-700 rounded-full w-16"></div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ResourceCardSkeleton; 