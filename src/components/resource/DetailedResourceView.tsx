import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Entity, ToolDetails, CourseDetails, AgencyDetails, ContentCreatorDetails, CommunityDetails, NewsletterDetails } from '@/types/entity';
import { Review } from '@/types/review';
import ReviewItem from './ReviewItem';
import { Star, ExternalLink, Tag as TagIcon, Layers as CategoryIcon, Briefcase as EntityTypeIcon, Zap, BookOpen, Users, FileText, Megaphone, Link as LinkIcon, CalendarDays, Target, Users2, Newspaper, Palette } from 'lucide-react'; // Added more icons
import { Button } from '@/components/ui/button';
import ReviewForm, { ReviewFormData } from './ReviewForm';
import { useAuth } from '@/contexts/AuthContext';

interface DetailedResourceViewProps {
  entity: Entity;
  reviews: Review[];
  onLoadMoreReviews: () => void;
  hasMoreReviews: boolean;
  isLoadingReviews: boolean;
  reviewsTotalCount?: number;
  onSubmitReview: (data: ReviewFormData) => Promise<void>;
  isSubmittingReview: boolean;
  reviewSubmissionError?: string | null;
  reviewSubmissionSuccess?: string | null;
}

// Helper to format field keys for display
const formatFieldKey = (key: string) => {
  return key
    .replace(/_/g, ' ')
    .replace(/\b\w/g, (char) => char.toUpperCase());
};

// Helper to render a list of strings (e.g., key features, prerequisites)
const renderStringList = (items?: string[], title?: string) => {
  if (!items || items.length === 0) return null;
  return (
    <div className="mt-2">
      {title && <p className="text-sm font-semibold text-gray-600 mb-1">{title}:</p>}
      <ul className="list-disc list-inside pl-1 space-y-0.5">
        {items.map((item, index) => (
          <li key={index} className="text-sm text-gray-600">{item}</li>
        ))}
      </ul>
    </div>
  );
};

const DetailedResourceView: React.FC<DetailedResourceViewProps> = ({ 
  entity, 
  reviews, 
  onLoadMoreReviews,
  hasMoreReviews,
  isLoadingReviews,
  reviewsTotalCount,
  onSubmitReview,
  isSubmittingReview,
  reviewSubmissionError,
  reviewSubmissionSuccess
}) => {
  const { session } = useAuth();
  const fallbackImage = '/images/placeholder-logo.png';
  const entityName = entity?.name || 'Unnamed Entity';

  const renderStars = (rating: number) => {
    const stars = [];
    if (typeof rating !== 'number') return <span className="text-gray-500 text-sm">Not rated</span>;
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Star
          key={i}
          className={`w-5 h-5 ${i <= rating ? 'text-yellow-400' : 'text-gray-300'}`}
          fill={i <= rating ? 'currentColor' : 'none'}
        />,
      );
    }
    return stars;
  };

  const renderEntitySpecificDetails = () => {
    if (!entity.details) return null;

    let detailsContent = null;
    const commonDetailItemClass = "text-sm text-gray-600 dark:text-gray-400";
    const commonDetailLabelClass = "font-semibold text-gray-700 dark:text-gray-300";
    let icon = <Palette className="w-5 h-5 mr-2 text-primary" />; // Default icon
    let detailsTitle = `${entity.entityType?.name || 'Additional'} Details`;

    switch (entity.entityType?.slug) {
      case 'tool':
        const toolDetails = entity.details as ToolDetails;
        icon = <Zap className="w-5 h-5 mr-2 text-primary" />;
        detailsTitle = "Tool Specifications";
        detailsContent = (
          <>
            {toolDetails.technical_level && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Technical Level:</span> {toolDetails.technical_level}</p>}
            {renderStringList(toolDetails.key_features, 'Key Features')}
            {renderStringList(toolDetails.use_cases, 'Use Cases')}
            {renderStringList(toolDetails.integrations, 'Integrations')}
            {toolDetails.pricing_model && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Pricing:</span> {toolDetails.pricing_model}</p>}
            {typeof toolDetails.api_available === 'boolean' && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>API Available:</span> {toolDetails.api_available ? 'Yes' : 'No'}</p>}
            {typeof toolDetails.self_hosted_option === 'boolean' && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Self-Hosted Option:</span> {toolDetails.self_hosted_option ? 'Yes' : 'No'}</p>}
          </>
        );
        break;
      case 'course':
        const courseDetails = entity.details as CourseDetails;
        icon = <BookOpen className="w-5 h-5 mr-2 text-primary" />;
        detailsTitle = "Course Information";
        detailsContent = (
          <>
            {courseDetails.instructor_name && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Instructor:</span> {courseDetails.instructor_name}</p>}
            {courseDetails.duration_text && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Duration:</span> {courseDetails.duration_text}</p>}
            {courseDetails.level && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Level:</span> {courseDetails.level}</p>}
            {renderStringList(courseDetails.prerequisites, 'Prerequisites')}
            {renderStringList(courseDetails.learning_outcomes, 'Learning Outcomes')}
            {courseDetails.language && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Language:</span> {courseDetails.language}</p>}
            {typeof courseDetails.certificate_available === 'boolean' && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Certificate:</span> {courseDetails.certificate_available ? 'Available' : 'Not Available'}</p>}
          </>
        );
        break;
      case 'agency':
        const agencyDetails = entity.details as AgencyDetails;
        icon = <EntityTypeIcon className="w-5 h-5 mr-2 text-primary" />;
        detailsTitle = "Agency Overview";
        detailsContent = (
          <>
            {renderStringList(agencyDetails.services_offered, 'Services Offered')}
            {renderStringList(agencyDetails.specializations, 'Specializations')}
            {agencyDetails.portfolio_url && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Portfolio:</span> <a href={agencyDetails.portfolio_url} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">View Portfolio <ExternalLink className='inline w-3 h-3 ml-0.5' /></a></p>}
            {agencyDetails.team_size && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Team Size:</span> {agencyDetails.team_size}</p>}
            {agencyDetails.region_served && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Region Served:</span> {agencyDetails.region_served}</p>}
            {agencyDetails.contact_email && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Contact:</span> <a href={`mailto:${agencyDetails.contact_email}`} className="text-primary hover:underline">{agencyDetails.contact_email}</a></p>}
          </>
        );
        break;
      case 'content-creator':
        const creatorDetails = entity.details as ContentCreatorDetails;
        icon = <Megaphone className="w-5 h-5 mr-2 text-primary" />;
        detailsTitle = "Creator Profile";
        detailsContent = (
          <>
            {creatorDetails.platform && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Main Platform:</span> {creatorDetails.platform}</p>}
            {creatorDetails.platform_url && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Platform Link:</span> <a href={creatorDetails.platform_url} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">View Platform <ExternalLink className='inline w-3 h-3 ml-0.5' /></a></p>}
            {creatorDetails.subscriber_count && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Subscribers/Followers:</span> {creatorDetails.subscriber_count}</p>}
            {renderStringList(creatorDetails.content_focus, 'Content Focus')}
            {creatorDetails.collaboration_email && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Collaborations:</span> <a href={`mailto:${creatorDetails.collaboration_email}`} className="text-primary hover:underline">{creatorDetails.collaboration_email}</a></p>}
            {creatorDetails.sample_work_links && creatorDetails.sample_work_links.length > 0 && (
                <div className="mt-2">
                    <p className={`${commonDetailLabelClass} mb-1`}>Sample Work:</p>
                    <ul className="list-disc list-inside pl-1 space-y-0.5">
                    {creatorDetails.sample_work_links.map((link, index) => (
                        <li key={index} className={commonDetailItemClass}>
                        <a href={link} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Sample Link {index + 1} <ExternalLink className='inline w-3 h-3 ml-0.5' /></a>
                        </li>
                    ))}
                    </ul>
                </div>
            )}
          </>
        );
        break;
      case 'community':
        const communityDetails = entity.details as CommunityDetails;
        icon = <Users2 className="w-5 h-5 mr-2 text-primary" />;
        detailsTitle = "Community Hub";
        detailsContent = (
          <>
            {communityDetails.platform_name && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Platform:</span> {communityDetails.platform_name}</p>}
            {communityDetails.platform_url && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Community Link:</span> <a href={communityDetails.platform_url} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Join Community <ExternalLink className='inline w-3 h-3 ml-0.5' /></a></p>}
            {communityDetails.member_count && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Members:</span> {communityDetails.member_count}</p>}
            {renderStringList(communityDetails.main_topics, 'Main Topics')}
            {communityDetails.moderator_info && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Moderator Info:</span> {communityDetails.moderator_info}</p>}
            {communityDetails.entry_requirements && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Entry Requirements:</span> {communityDetails.entry_requirements}</p>}
          </>
        );
        break;
      case 'newsletter':
        const newsletterDetails = entity.details as NewsletterDetails;
        icon = <Newspaper className="w-5 h-5 mr-2 text-primary" />;
        detailsTitle = "Newsletter Info";
        detailsContent = (
          <>
            {newsletterDetails.author_name && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Author:</span> {newsletterDetails.author_name}</p>}
            {newsletterDetails.publication_schedule && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Schedule:</span> {newsletterDetails.publication_schedule}</p>}
            {newsletterDetails.target_audience && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Target Audience:</span> {newsletterDetails.target_audience}</p>}
            {renderStringList(newsletterDetails.topics_covered, 'Topics Covered')}
            {newsletterDetails.archive_url && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Archive:</span> <a href={newsletterDetails.archive_url} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">View Archive <ExternalLink className='inline w-3 h-3 ml-0.5' /></a></p>}
            {newsletterDetails.subscription_link && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Subscribe:</span> <a href={newsletterDetails.subscription_link} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Subscribe Here <ExternalLink className='inline w-3 h-3 ml-0.5' /></a></p>}
          </>
        );
        break;
      default:
        if (Object.keys(entity.details).length > 0) {
            detailsTitle = `Additional Details`; // Keep a generic title if type is unknown but details exist
            detailsContent = (
                <>
                {Object.entries(entity.details).map(([key, value]) => (
                    <p key={key} className={commonDetailItemClass}>
                    <span className={commonDetailLabelClass}>{formatFieldKey(key)}:</span> {String(value)}
                    </p>
                ))}
                </>
            );
        }
        break;
    }

    if (!detailsContent) return null;

    return (
      <div className="mt-6 py-6 border-t border-gray-200 dark:border-gray-700">
        <h3 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center">
          {icon} {detailsTitle} {/* Use dynamic title and icon */}
        </h3>
        <div className="space-y-3">{detailsContent}</div> {/* Added space-y-3 for better spacing within details */}
      </div>
    );
  };

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12 max-w-5xl"> {/* Wider container */}
      <div className="bg-white dark:bg-gray-800 shadow-xl rounded-lg p-6 sm:p-8 md:p-10"> {/* Increased padding */}
        {/* Header Section */}
        <div className="md:flex md:items-start md:space-x-8 mb-8 pb-8 border-b border-gray-200 dark:border-gray-700"> {/* Increased spacing */}
          <div className="relative w-full md:w-52 h-52 md:flex-shrink-0 mb-6 md:mb-0 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center p-3">
            <Image
              src={entity.logoUrl || fallbackImage}
              alt={`${entityName} logo`}
              width={192} // Slightly larger image for more prominence
              height={192}
              objectFit="contain"
              className="rounded-md"
            />
          </div>
          <div className="flex-grow mt-2 md:mt-0">
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-3">{entityName}</h1>
            
            <div className="space-y-3 mb-5">
                {entity.websiteUrl && (
                <Link href={entity.websiteUrl} passHref legacyBehavior>
                    <a target="_blank" rel="noopener noreferrer" className="text-primary hover:text-primary-dark dark:text-primary-light dark:hover:text-primary inline-flex items-center text-base font-medium">
                    Visit Website <ExternalLink className="w-4 h-4 ml-1.5" />
                    </a>
                </Link>
                )}
                <div className="flex items-center">
                    <div className="flex items-center mr-4">{renderStars(entity.averageRating)}</div>
                    <span className="text-sm text-gray-600 dark:text-gray-400">({entity.reviewCount} Reviews)</span>
                </div>
                <div className="flex flex-wrap items-center gap-x-3 gap-y-2 text-sm text-gray-600 dark:text-gray-400">
                    <span className="inline-flex items-center">
                        <EntityTypeIcon className="w-4 h-4 mr-1.5 text-gray-500 dark:text-gray-400" />
                        {entity.entityType?.name || 'N/A'}
                    </span>
                    <span className="inline-flex items-center">
                        <CategoryIcon className="w-4 h-4 mr-1.5 text-gray-500 dark:text-gray-400" />
                        {entity.category?.name || 'N/A'}
                    </span>
                </div>
            </div>
          </div>
        </div>

        {/* Description Section */}
        {entity.description && (
            <div className="mb-8 py-6">
                <h3 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-4">About {entityName}</h3>
                <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap leading-relaxed text-base">
                {entity.description}
                </p>
            </div>
        )}

        {/* Entity Specific Details */}
        {renderEntitySpecificDetails()}

        {/* Tags Section */}
        {entity.tags && entity.tags.length > 0 && (
          <div className="my-8 py-6 border-t border-gray-200 dark:border-gray-700">
            <h3 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-4">Tags</h3>
            <div className="flex flex-wrap gap-2">
              {entity.tags.map((tag) => (
                <span
                  key={tag.id}
                  className="inline-flex items-center text-sm font-medium py-1.5 px-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full"
                >
                  <TagIcon className="w-3.5 h-3.5 mr-1.5 text-gray-500 dark:text-gray-400" />
                  {tag.name}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Reviews Section */}
        <div className="mt-8 py-6 border-t border-gray-200 dark:border-gray-700">
          <h3 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-6">
            User Reviews ({reviews?.length || 0}
            {reviewsTotalCount && reviewsTotalCount > (reviews?.length || 0) ? ` of ${reviewsTotalCount}` : ''})
          </h3>
          {reviews && reviews.length > 0 ? (
            <div className="space-y-6">
                {reviews.map((review) => (
                <ReviewItem key={review.id} review={review} />
                ))}
            </div>
          ) : (
            !isLoadingReviews && <p className="text-gray-600 dark:text-gray-400">No reviews yet for {entityName}. Be the first to share your thoughts!</p>
          )}
          {isLoadingReviews && (
            <p className="text-center text-gray-500 dark:text-gray-400 py-4">Loading reviews...</p>
          )}
          {hasMoreReviews && !isLoadingReviews && (
            <div className="mt-8 text-center">
              <Button onClick={onLoadMoreReviews} variant="outline" size="lg">
                Load More Reviews
              </Button>
            </div>
          )}
        </div>

        {/* Write a Review Section (Authenticated users only) */}
        {session && (
          <div className="mt-8 py-8 border-t border-gray-200 dark:border-gray-700">
            <h3 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-6">Write a Review for {entityName}</h3>
            <ReviewForm 
              entityId={entity.id} 
              onSubmitReview={onSubmitReview}
              isSubmitting={isSubmittingReview}
              formError={reviewSubmissionError}
              formSuccess={reviewSubmissionSuccess}
            />
          </div>
        )}
        {!session && (
           <div className="mt-8 py-8 border-t border-gray-200 dark:border-gray-700 text-center">
             <p className="text-gray-600 dark:text-gray-400">Please <Link href="/login" className="text-primary hover:underline">log in</Link> to write a review.</p>
           </div>
        )}

      </div>
    </div>
  );
};

export default DetailedResourceView; 