import React from 'react';
import { Review } from '@/types/review';
import { Star, UserCircle } from 'lucide-react'; // Assuming UserCircle for avatar placeholder

interface ReviewItemProps {
  review: Review;
}

const ReviewItem: React.FC<ReviewItemProps> = ({ review }) => {
  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Star
          key={i}
          className={`w-5 h-5 ${i <= rating ? 'text-yellow-400' : 'text-gray-300'}`}
          fill={i <= rating ? 'currentColor' : 'none'}
        />,
      );
    }
    return stars;
  };

  const reviewerName = review.user?.display_name || review.user?.username || 'Anonymous User';
  const reviewerAvatar = review.user?.profile_picture_url; // Or a default

  return (
    <div className="bg-white p-6 rounded-lg shadow mb-6">
      <div className="flex items-center mb-3">
        {reviewerAvatar ? (
          <img src={reviewerAvatar} alt={reviewerName} className="w-10 h-10 rounded-full mr-3" />
        ) : (
          <UserCircle className="w-10 h-10 text-gray-400 mr-3" />
        )}
        <div>
          <p className="font-semibold text-gray-800">{reviewerName}</p>
          {review.createdAt && (
            <p className="text-xs text-gray-500">
              {new Date(review.createdAt).toLocaleDateString()}
            </p>
          )}
        </div>
      </div>
      <div className="flex items-center mb-2">{renderStars(review.rating)}</div>
      {review.title && <h4 className="text-lg font-semibold text-gray-700 mb-1">{review.title}</h4>}
      <p className="text-gray-600 text-sm whitespace-pre-wrap">{review.text}</p>
    </div>
  );
};

export default ReviewItem; 