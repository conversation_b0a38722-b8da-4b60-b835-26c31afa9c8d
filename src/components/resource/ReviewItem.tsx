import React from 'react';
import { Review } from '@/types/review';
import { Star, UserCircle } from 'lucide-react';

interface ReviewItemProps {
  review: Review;
}

const ReviewItem: React.FC<ReviewItemProps> = ({ review }) => {
  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Star
          key={i}
          className={`w-5 h-5 ${i <= rating ? 'text-yellow-400' : 'text-muted-foreground/30'}`}
          fill={i <= rating ? 'currentColor' : 'none'}
        />,
      );
    }
    return stars;
  };

  const reviewerName = review.user?.display_name || review.user?.username || 'Anonymous User';
  const reviewerAvatar = review.user?.profile_picture_url;

  return (
    <article className="bg-card dark:bg-gray-800 p-6 rounded-lg shadow border border-border dark:border-gray-700/50">
      <div className="flex items-center mb-4">
        {reviewerAvatar ? (
          <img src={reviewerAvatar} alt={reviewerName} className="w-10 h-10 rounded-full mr-4" />
        ) : (
          <UserCircle className="w-10 h-10 text-muted-foreground mr-4" />
        )}
        <div>
          <p className="font-semibold text-foreground dark:text-white">{reviewerName}</p>
          {review.createdAt && (
            <p className="text-xs text-muted-foreground dark:text-gray-400">
              {new Date(review.createdAt).toLocaleDateString()}
            </p>
          )}
        </div>
      </div>
      <div className="flex items-center mb-3">{renderStars(review.rating)}</div>
      {review.title && <h4 className="text-lg font-semibold text-foreground dark:text-gray-200 mb-2">{review.title}</h4>}
      <p className="text-muted-foreground dark:text-gray-300 text-sm whitespace-pre-wrap leading-relaxed">{review.text}</p>
    </article>
  );
};

export default ReviewItem; 