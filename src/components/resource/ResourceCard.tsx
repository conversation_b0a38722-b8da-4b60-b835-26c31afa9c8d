import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Entity } from '@/types/entity';
import { Star, ArrowRight, Heart, Share2 } from 'lucide-react'; // Added Heart and Share2

interface ResourceCardProps {
  entity: Entity;
}

const ResourceCard: React.FC<ResourceCardProps> = ({ entity }) => {
  console.log("ResourceCard - Received entity prop:", entity);

  if (!entity) {
    console.error("ResourceCard - Received undefined entity prop. Rendering null.");
    return null;
  }

  const fallbackImage = '/images/placeholder-logo.png';
  const entityName = entity?.name || 'Unnamed Entity';
  const entityDescription = entity?.description || 'No description available.';
  const entityTypeName = entity?.entityType?.name || 'N/A';
  const categoryName = entity?.category?.name || 'N/A';
  const averageRating = typeof entity?.averageRating === 'number' ? entity.averageRating.toFixed(1) : 'N/A';
  const reviewCount = typeof entity?.reviewCount === 'number' ? entity.reviewCount : 0;

  return (
    <div className="bg-white shadow-lg rounded-lg overflow-hidden transition-transform duration-300 hover:scale-105 flex flex-col h-full">
      <Link href={`/entities/${entity?.id || '#'}`} passHref legacyBehavior>
        <a className="block hover:cursor-pointer">
          <div className="relative w-full h-48 bg-gray-200">
            <Image
              src={entity?.logoUrl || fallbackImage}
              alt={`${entityName} logo`}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              style={{ objectFit: "contain" }}
              className="p-4"
            />
          </div>
        </a>
      </Link>

      <div className="p-6 flex flex-col flex-grow">
        <Link href={`/entities/${entity?.id || '#'}`} passHref legacyBehavior>
          <a className="block hover:cursor-pointer">
            <h3 className="text-xl font-semibold mb-2 text-gray-800 hover:text-primary truncate" title={entityName}>
              {entityName}
            </h3>
          </a>
        </Link>

        <div className="mb-3 space-x-2">
          <span className="text-xs font-semibold inline-block py-1 px-2.5 uppercase rounded-full text-primary-dark bg-primary-light">
            {entityTypeName}
          </span>
          <span className="text-xs font-semibold inline-block py-1 px-2.5 uppercase rounded-full text-secondary-dark bg-secondary-light">
            {categoryName}
          </span>
        </div>

        <p className="text-gray-600 text-sm mb-4 line-clamp-3 flex-grow" title={entityDescription}>
          {entityDescription}
        </p>

        <div className="mt-auto">
          <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
            <div className="flex items-center">
              <Star className="w-4 h-4 text-yellow-400 mr-1" />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {averageRating} 
                <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">({reviewCount} reviews)</span>
              </span>
            </div>
            {/* Placeholder Quick Action Buttons */}
            <div className="flex items-center space-x-2">
              <button 
                aria-label="Bookmark resource" 
                className="p-1.5 text-gray-500 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-150 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                onClick={(e) => { e.stopPropagation(); e.preventDefault(); console.log('Bookmark clicked for:', entity?.id); /* Implement actual bookmark logic later */ }}
              >
                <Heart className="w-4 h-4" />
              </button>
              <button 
                aria-label="Share resource" 
                className="p-1.5 text-gray-500 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-150 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                onClick={(e) => { e.stopPropagation(); e.preventDefault(); console.log('Share clicked for:', entity?.id); /* Implement actual share logic later */ }}
              >
                <Share2 className="w-4 h-4" />
              </button>
            </div>
          </div>

          {entity?.tags && entity.tags.length > 0 && (
            <div className="mb-4">
              {entity.tags.slice(0, 3).map((tag) => (
                <span
                  key={tag?.id || Math.random()} // Added fallback key
                  className="text-xs inline-block mr-1 mb-1 py-1 px-2.5 leading-none text-center whitespace-nowrap align-baseline font-bold bg-gray-200 text-gray-700 rounded-full"
                >
                  {tag?.name || 'Unnamed Tag'}
                </span>
              ))}
              {entity.tags.length > 3 && (
                <span className="text-xs inline-block py-1 px-2.5 leading-none text-center whitespace-nowrap align-baseline font-bold bg-gray-200 text-gray-700 rounded-full">
                  +{entity.tags.length - 3}
                </span>
              )}
            </div>
          )}

          <Link 
            href={`/entities/${entity.id}`}
            className="inline-flex items-center text-sm font-medium text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 group py-2 px-3 rounded-md hover:bg-primary-50 dark:hover:bg-gray-700 transition-all duration-150"
          >
            View Details
            <ArrowRight className="ml-1.5 h-4 w-4 transform transition-transform duration-150 group-hover:translate-x-0.5" />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ResourceCard; 