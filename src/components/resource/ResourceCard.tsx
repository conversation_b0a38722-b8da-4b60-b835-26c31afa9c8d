import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Entity } from '@/types/entity';
import { Star, ArrowRight, Heart, Share2, ExternalLink, Bookmark } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ResourceCardProps {
  entity: Entity;
  onBookmark?: (entityId: string) => void;
  onShare?: (entity: Entity) => void;
  isBookmarked?: boolean;
}

const ResourceCard: React.FC<ResourceCardProps> = ({
  entity,
  onBookmark,
  onShare,
  isBookmarked = false
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [imageError, setImageError] = useState(false);

  if (!entity) {
    console.error("ResourceCard - Received undefined entity prop. Rendering null.");
    return null;
  }

  const fallbackImage = '/images/placeholder-logo.png';
  const entityName = entity?.name || 'Unnamed Entity';
  const entityDescription = entity?.description || 'No description available.';
  const entityTypeName = entity?.entityType?.name || 'N/A';
  const categoryName = entity?.category?.name || 'N/A';
  const averageRating = typeof entity?.averageRating === 'number' ? entity.averageRating.toFixed(1) : 'N/A';
  const reviewCount = typeof entity?.reviewCount === 'number' ? entity.reviewCount : 0;

  const handleBookmark = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onBookmark?.(entity.id);
  };

  const handleShare = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onShare?.(entity);
  };

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <div
      className="group relative bg-card border border-border rounded-lg overflow-hidden card-hover flex flex-col h-full"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Image Section */}
      <Link href={`/entities/${entity?.id || '#'}`} className="block">
        <div className="relative w-full h-48 bg-muted/50 overflow-hidden">
          <Image
            src={imageError ? fallbackImage : (entity?.logoUrl || fallbackImage)}
            alt={`${entityName} logo`}
            fill
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            style={{ objectFit: "contain" }}
            className="p-4 transition-transform duration-200 group-hover:scale-105"
            onError={handleImageError}
          />

          {/* Quick Actions Overlay - Visible on Hover */}
          <div className={`absolute top-3 right-3 flex gap-2 transition-opacity duration-200 ${
            isHovered ? 'opacity-100' : 'opacity-0'
          }`}>
            <Button
              size="sm"
              variant="secondary"
              className="h-8 w-8 p-0 bg-background/80 backdrop-blur-sm hover:bg-background"
              onClick={handleBookmark}
              aria-label={isBookmarked ? "Remove bookmark" : "Add bookmark"}
            >
              <Bookmark className={`h-4 w-4 ${isBookmarked ? 'fill-current text-primary' : ''}`} />
            </Button>
            <Button
              size="sm"
              variant="secondary"
              className="h-8 w-8 p-0 bg-background/80 backdrop-blur-sm hover:bg-background"
              onClick={handleShare}
              aria-label="Share resource"
            >
              <Share2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </Link>

      {/* Content Section */}
      <div className="p-6 flex flex-col flex-grow">
        <Link href={`/entities/${entity?.id || '#'}`}>
          <h3 className="text-lg font-semibold mb-3 text-card-foreground hover:text-primary transition-colors line-clamp-2" title={entityName}>
            {entityName}
          </h3>
        </Link>

        {/* Type and Category Badges */}
        <div className="mb-4 flex flex-wrap gap-2">
          <span className="inline-flex items-center px-2.5 py-1 text-xs font-medium bg-primary/10 text-primary rounded-full border border-primary/20">
            {entityTypeName}
          </span>
          <span className="inline-flex items-center px-2.5 py-1 text-xs font-medium bg-secondary/10 text-secondary rounded-full border border-secondary/20">
            {categoryName}
          </span>
        </div>

        {/* Description */}
        <p className="text-muted-foreground text-sm mb-4 line-clamp-3 flex-grow" title={entityDescription}>
          {entityDescription}
        </p>

        {/* Footer Section */}
        <div className="mt-auto space-y-4">
          {/* Rating and Stats */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <Star className="w-4 h-4 text-secondary fill-current" />
              <span className="text-sm font-medium text-card-foreground">
                {averageRating}
              </span>
              <span className="text-xs text-muted-foreground">
                ({reviewCount} {reviewCount === 1 ? 'review' : 'reviews'})
              </span>
            </div>

            {/* External Link Indicator */}
            {entity?.websiteUrl && (
              <ExternalLink className="w-4 h-4 text-muted-foreground" />
            )}
          </div>

          {/* Tags */}
          {entity?.tags && entity.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {entity.tags.slice(0, 3).map((tag) => (
                <span
                  key={tag?.id || Math.random()}
                  className="inline-flex items-center px-2 py-1 text-xs font-medium bg-muted text-muted-foreground rounded-md"
                >
                  {tag?.name || 'Unnamed Tag'}
                </span>
              ))}
              {entity.tags.length > 3 && (
                <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-muted text-muted-foreground rounded-md">
                  +{entity.tags.length - 3} more
                </span>
              )}
            </div>
          )}

          {/* CTA Button */}
          <Link
            href={`/entities/${entity.id}`}
            className="inline-flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-primary bg-primary/10 border border-primary/20 rounded-md hover:bg-primary/20 transition-colors duration-150 group"
          >
            View Details
            <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-150 group-hover:translate-x-0.5" />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ResourceCard; 