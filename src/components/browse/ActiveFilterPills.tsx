'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { XIcon, FilterIcon } from 'lucide-react';

interface FilterPill {
  id: string;
  name: string;
  type: 'entityTypeIds' | 'categoryIds' | 'tagIds' | 'featureIds';
}

interface ActiveFilterPillsProps {
  filters: FilterPill[];
  onRemove: (id: string, type: FilterPill['type']) => void;
  onClearAll: () => void;
  searchTerm?: string;
  onClearSearch?: () => void;
}

const ActiveFilterPills: React.FC<ActiveFilterPillsProps> = ({
  filters,
  onRemove,
  onClearAll,
  searchTerm,
  onClearSearch
}) => {
  const hasActiveFilters = filters.length > 0 || searchTerm;

  if (!hasActiveFilters) {
    return null;
  }

  const getTypeLabel = (type: FilterPill['type']) => {
    switch (type) {
      case 'entityTypeIds': return 'Type';
      case 'categoryIds': return 'Category';
      case 'tagIds': return 'Tag';
      case 'featureIds': return 'Feature';
      default: return '';
    }
  };

  const getTypeColor = (type: FilterPill['type']) => {
    switch (type) {
      case 'entityTypeIds': return 'bg-primary/10 text-primary border-primary/20';
      case 'categoryIds': return 'bg-secondary/10 text-secondary border-secondary/20';
      case 'tagIds': return 'bg-success/10 text-success border-success/20';
      case 'featureIds': return 'bg-purple-100 text-purple-700 border-purple-200 dark:bg-purple-900/20 dark:text-purple-300 dark:border-purple-800';
      default: return 'bg-muted text-muted-foreground border-border';
    }
  };

  return (
    <div className="flex flex-wrap items-center gap-3 p-4 bg-muted/30 rounded-lg border border-border">
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <FilterIcon className="h-4 w-4" />
        <span className="font-medium">Active filters:</span>
      </div>

      <div className="flex flex-wrap items-center gap-2">
        {/* Search Term Pill */}
        {searchTerm && (
          <div className="inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium bg-accent text-accent-foreground rounded-full border border-border">
            <span className="text-xs text-muted-foreground">Search:</span>
            <span className="truncate max-w-32" title={searchTerm}>
              "{searchTerm}"
            </span>
            <Button
              size="sm"
              variant="ghost"
              onClick={onClearSearch}
              className="h-4 w-4 p-0 hover:bg-destructive/20 hover:text-destructive rounded-full"
              aria-label="Clear search"
            >
              <XIcon className="h-3 w-3" />
            </Button>
          </div>
        )}

        {/* Filter Pills */}
        {filters.map((filter) => (
          <div
            key={`${filter.type}-${filter.id}`}
            className={`inline-flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium rounded-full border transition-colors ${getTypeColor(filter.type)}`}
          >
            <span className="text-xs opacity-75">
              {getTypeLabel(filter.type)}:
            </span>
            <span className="truncate max-w-32" title={filter.name}>
              {filter.name}
            </span>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onRemove(filter.id, filter.type)}
              className="h-4 w-4 p-0 hover:bg-destructive/20 hover:text-destructive rounded-full"
              aria-label={`Remove ${filter.name} filter`}
            >
              <XIcon className="h-3 w-3" />
            </Button>
          </div>
        ))}
      </div>

      {/* Clear All Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={onClearAll}
        className="ml-auto text-xs h-8 px-3 hover:bg-destructive/10 hover:text-destructive hover:border-destructive/20"
      >
        Clear all
      </Button>
    </div>
  );
};

export default ActiveFilterPills;
