'use client';

import React from 'react';
import { X } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

interface FilterPill {
  id: string;
  name: string;
  type: 'entityTypeIds' | 'categoryIds' | 'tagIds' | 'featureIds';
}

interface ActiveFiltersProps {
  filters: FilterPill[];
  onRemove: (id: string, type: FilterPill['type']) => void;
  onClearAll: () => void;
  searchTerm?: string;
  onClearSearch?: () => void;
}

const ActiveFilters: React.FC<ActiveFiltersProps> = ({
  filters,
  onRemove,
  onClearAll,
  searchTerm,
  onClearSearch
}) => {
  const hasActiveFilters = filters.length > 0 || searchTerm;

  if (!hasActiveFilters) {
    return null;
  }

  const getTypeColor = (type: FilterPill['type']) => {
    switch (type) {
      case 'entityTypeIds': 
        return 'bg-indigo-100 text-indigo-700 hover:bg-indigo-200 dark:bg-indigo-900/20 dark:text-indigo-300';
      case 'categoryIds': 
        return 'bg-amber-100 text-amber-700 hover:bg-amber-200 dark:bg-amber-900/20 dark:text-amber-300';
      case 'tagIds': 
        return 'bg-emerald-100 text-emerald-700 hover:bg-emerald-200 dark:bg-emerald-900/20 dark:text-emerald-300';
      case 'featureIds': 
        return 'bg-purple-100 text-purple-700 hover:bg-purple-200 dark:bg-purple-900/20 dark:text-purple-300';
      default: 
        return 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
      <div className="flex flex-wrap items-center gap-3">
        <span className="text-sm font-medium text-gray-600">
          Active filters:
        </span>

        <div className="flex flex-wrap items-center gap-2">
          {/* Search Term Badge */}
          {searchTerm && (
            <Badge
              variant="secondary"
              className="bg-gray-100 text-gray-700 hover:bg-gray-200 cursor-pointer transition-colors"
              onClick={onClearSearch}
            >
              Search: "{searchTerm}"
              <X className="ml-1 h-3 w-3" />
            </Badge>
          )}

          {/* Filter Pills */}
          {filters.map((filter) => (
            <Badge
              key={`${filter.type}-${filter.id}`}
              variant="secondary"
              className={`cursor-pointer transition-colors ${getTypeColor(filter.type)}`}
              onClick={() => onRemove(filter.id, filter.type)}
            >
              {filter.name}
              <X className="ml-1 h-3 w-3" />
            </Badge>
          ))}
        </div>

        {/* Clear All Button */}
        <Button
          variant="outline"
          size="sm"
          onClick={onClearAll}
          className="ml-auto text-xs h-7 px-3 text-gray-600 hover:text-red-600 hover:border-red-200"
        >
          Clear all
        </Button>
      </div>
    </div>
  );
};

export default ActiveFilters;
