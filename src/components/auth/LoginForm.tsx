"use client";

import { use<PERSON><PERSON>, SubmitHand<PERSON> } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useRouter } from "next/navigation";
import Link from 'next/link';
import { But<PERSON> } from "@/components/ui/button";
import Input from "@/components/forms/Input";
import { loginUser, LoginCredentials, AuthResponse } from "@/services/api";
import { createSupabaseBrowserClient } from "@/lib/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

const LoginSchema = z.object({
  email: z.string().email({ message: "Invalid email address" }),
  password: z.string().min(6, { message: "Password must be at least 6 characters" }),
});

type LoginFormInputs = z.infer<typeof LoginSchema>;

const LoginForm = () => {
  const router = useRouter();
  const { authError, setAuthError } = useAuth();
  const supabaseClient = createSupabaseBrowserClient();
  const form = useForm<LoginFormInputs>({
    resolver: zodResolver(LoginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });
  const { setError, clearErrors } = form;

  const onSubmit = async (values: LoginFormInputs) => {
    setAuthError(null);
    clearErrors("root.serverError");
    try {
      const result: AuthResponse = await loginUser(values);

      if (result.session?.access_token && result.session?.refresh_token) {
        const { data: setSessionData, error: setSessionError } = await supabaseClient.auth.setSession({
          access_token: result.session.access_token,
          refresh_token: result.session.refresh_token,
        });

        if (setSessionError) {
          console.error('[LoginForm] Supabase setSession error:', setSessionError);
          const errorMessage = "Failed to set session with Supabase: " + setSessionError.message;
          setAuthError(errorMessage);
          setError("root.serverError", { type: "custom", message: errorMessage });
        } else {
          router.push('/'); 
          router.refresh();
        }
      } else {
        const errorMessage = "Login successful, but session data is incomplete or missing tokens. Please try again.";
        console.warn('[LoginForm] Incomplete session data post-API call:', result.session);
        setAuthError(errorMessage);
        setError("root.serverError", { type: "custom", message: errorMessage });
      }
    } catch (error: any) {
      console.error("[LoginForm] Login submission error:", error.message, error.details || error);
      let specificMessage = error.message || "An unexpected error occurred. Please try again.";
      setAuthError(specificMessage);
      setError("root.serverError", { type: "custom", message: specificMessage });
    }
  };

  const handleGoogleSignIn = async () => {
    setAuthError(null);
    clearErrors("root.serverError");
    const { error } = await supabaseClient.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`,
      },
    });
    if (error) {
      console.error('[LoginForm] Google Sign In error:', error);
      setAuthError(error.message);
      setError("root.serverError", { type: "custom", message: error.message });
    }
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 w-full max-w-md p-8 bg-white shadow-lg rounded-lg">
      <h2 className="text-3xl font-bold text-center text-gray-800">Login</h2>
      
      {form.formState.errors.root?.serverError && (
        <div className="p-3 mb-4 text-sm text-red-700 bg-red-100 rounded-lg" role="alert">
          {form.formState.errors.root.serverError.message}
        </div>
      )}
      {authError && !form.formState.errors.root?.serverError && (
        <div className="p-3 mb-4 text-sm text-red-700 bg-red-100 rounded-lg" role="alert">
          {authError}
        </div>
      )}
      {form.formState.errors.root && !form.formState.errors.root.serverError && !authError && (
        <div className="p-3 mb-4 text-sm text-red-700 bg-red-100 rounded-lg" role="alert">
          {form.formState.errors.root.message}
        </div>
      )}
      <div>
        <Input
          label="Email"
          type="email"
          {...form.register("email")}
          error={form.formState.errors.email}
          classNameContainer="mb-4"
          classNameInput="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 transition-shadow text-gray-900 placeholder-gray-500"
          classNameLabel="block mb-1 text-sm font-medium text-gray-700"
          classNameError="mt-1 text-xs text-red-600"
          autoComplete="email"
          placeholder="<EMAIL>"
        />
      </div>

      <div>
        <Input
          label="Password"
          type="password"
          {...form.register("password")}
          error={form.formState.errors.password}
          classNameContainer="mb-6"
          classNameInput="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 transition-shadow text-gray-900 placeholder-gray-500"
          classNameLabel="block mb-1 text-sm font-medium text-gray-700"
          classNameError="mt-1 text-xs text-red-600"
          autoComplete="current-password"
          placeholder="••••••••"
        />
      </div>

      <Button 
        type="submit" 
        disabled={form.formState.isSubmitting}
        className="w-full px-4 py-2 text-white bg-blue-600 hover:bg-blue-700 rounded-lg focus:ring-4 focus:ring-blue-300 transition-transform active:scale-95 disabled:opacity-50"
      >
        {form.formState.isSubmitting ? "Logging in..." : "Login"}
      </Button>

      <div className="mt-2">
        <Button 
          type="button" 
          onClick={handleGoogleSignIn}
          className="w-full px-4 py-2 text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 rounded-lg focus:ring-4 focus:ring-blue-300 transition-transform active:scale-95 flex items-center justify-center space-x-2"
          disabled={form.formState.isSubmitting}
        >
          <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
            <path d="M22.56,12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26,1.37-1.04,2.53-2.21,3.31v2.77h3.57c2.08-1.92,3.28-4.74,3.28-8.09Z" fill="#4285F4"/>
            <path d="M12,23c2.97,0,5.46-.98,7.28-2.66l-3.57-2.77c-.98,.66-2.23,1.06-3.71,1.06-2.86,0-5.29-1.93-6.16-4.53H2.18v2.84C3.99,20.53,7.7,23,12,23Z" fill="#34A853"/>
            <path d="M5.84,14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43,.35-2.09V7.07H2.18C1.43,8.55,1,10.22,1,12s.43,3.45,1.18,4.93l2.85-2.22.81-.62Z" fill="#FBBC05"/>
            <path d="M12,5.38c1.62,0,3.06,.56,4.21,1.64l3.15-3.15C17.45,2.29,14.97,1,12,1,7.7,1,3.99,3.47,2.18,7.07l3.66,2.84c.87-2.6,3.3-4.53,6.16-4.53Z" fill="#EA4335"/>
          </svg>
          <span>Sign in with Google</span>
        </Button>
      </div>

      <p className="text-sm text-center text-gray-600 pt-4">
        Don&apos;t have an account? <Link href="/register" className="font-medium text-blue-600 hover:underline">Sign up</Link>
      </p>
    </form>
  );
};

export default LoginForm; 