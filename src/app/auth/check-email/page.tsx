'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { MailCheck } from 'lucide-react';
// No resend functionality implemented yet, so removing related state and commented code

export default function CheckEmailPage() {
  const [email, setEmail] = useState<string | null>(null);
  // const [message, setMessage] = useState<string>(''); // For resend feedback - removed
  // const [error, setError] = useState<string>(''); // For resend feedback - removed

  useEffect(() => {
    const storedEmail = sessionStorage.getItem('registrationEmail');
    if (storedEmail) {
      setEmail(storedEmail);
      // Optional: Clear it if you don't want it to persist beyond one view
      // sessionStorage.removeItem('registrationEmail'); 
    }
  }, []);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 bg-white p-10 shadow-xl rounded-lg text-center">
        <div>
          <MailCheck className="mx-auto h-16 w-16 text-green-500 mb-4" strokeWidth={1.5} />
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Confirm Your Email
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Thanks for registering{email ? <span className="font-medium">, {email}</span> : ''}!
          </p>
          <p className="mt-2 text-center text-sm text-gray-600">
            We've sent a confirmation link to your email address. Please check your inbox (and spam folder) to complete your registration.
          </p>
          <p className="mt-2 text-center text-sm text-gray-600">
            Once confirmed, you can log in to your account.
          </p>
        </div>

        {/* Removed message and error display related to resend functionality */}

        <div className="mt-8">
          <Link href="/login">
            <Button variant="outline" className="w-full">
              Go to Login Page
            </Button>
          </Link>
        </div>

        <p className="mt-4 text-center text-xs text-gray-500">
          If you don't see the email, please give it a few minutes or check your spam/junk folder.
        </p>
      </div>
    </div>
  );
} 