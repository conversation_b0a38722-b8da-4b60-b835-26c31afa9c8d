'use client';

import React, { useEffect, useState, useCallback, useMemo, useRef } from 'react';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import ResourceCard from '@/components/resource/ResourceCard';
import SmartFilterSection from '@/components/browse/SmartFilterSection';
import ActiveFilterPills from '@/components/browse/ActiveFilterPills';
import { getEntities, getEntityTypes, getCategories, getTags } from '@/services/api';
import { Entity, EntityType, Category, Tag, PaginationMeta, GetEntitiesParams } from '@/types/entity';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { SearchIcon, FilterIcon, XIcon, SlidersHorizontalIcon } from 'lucide-react';
import ResourceCardSkeleton from '@/components/resource/ResourceCardSkeleton';

const ITEMS_PER_PAGE = 9; // Adjusted for potentially larger cards or 3-column layout

const SORT_OPTIONS = [
  { value: 'name', label: 'Name' },
  { value: 'createdAt', label: 'Date Added' },
  { value: 'averageRating', label: 'Rating' },
  // { value: 'popularity', label: 'Popularity' }, // Removed due to backend error
  // Add more options as needed, e.g., popularity, reviewCount
];

// Placeholder data for "Features" filter - to be replaced with actual data source
const FEATURES_FILTER_OPTIONS = [
  { id: 'free-tier', name: 'Free Tier Available', count: 234 },
  { id: 'api-access', name: 'API Access', count: 145 },
  { id: 'open-source', name: 'Open Source', count: 67 },
  // Add more features as needed
];

export default function BrowsePage() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { session } = useAuth();

  const [entities, setEntities] = useState<Entity[]>([]);
  const [allEntityTypes, setAllEntityTypes] = useState<EntityType[]>([]); // To store all available entity types
  const [allCategories, setAllCategories] = useState<Category[]>([]); // New state for categories
  const [allTags, setAllTags] = useState<Tag[]>([]); // New state for tags
  const [paginationMeta, setPaginationMeta] = useState<PaginationMeta | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingFilters, setIsLoadingFilters] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLoadingMore, setIsLoadingMore] = useState(false); // For infinite scroll

  // State for filter section expansion
  const [entityTypesExpanded, setEntityTypesExpanded] = useState(false);
  const [categoriesExpanded, setCategoriesExpanded] = useState(false);
  const [featuresExpanded, setFeaturesExpanded] = useState(false);
  const [tagsExpanded, setTagsExpanded] = useState(false);

  // State for filter search terms
  const [entityTypeSearch, setEntityTypeSearch] = useState('');
  const [categorySearch, setCategorySearch] = useState('');
  const [featureSearch, setFeatureSearch] = useState('');
  const [tagSearch, setTagSearch] = useState('');

  // State for mobile filter sidebar visibility
  const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);

  // Derived state from URL search parameters
  const searchTerm = useMemo(() => searchParams?.get('searchTerm') || '', [searchParams]);
  const selectedEntityTypeIds = useMemo(() => searchParams?.getAll('entityTypeIds') || [], [searchParams]);
  const selectedCategoryIds = useMemo(() => searchParams?.getAll('categoryIds') || [], [searchParams]); // New
  const selectedTagIds = useMemo(() => searchParams?.getAll('tagIds') || [], [searchParams]); // New
  const selectedFeatureIds = useMemo(() => searchParams?.getAll('featureIds') || [], [searchParams]); // New for features
  const currentPage = useMemo(() => parseInt(searchParams?.get('page') || '1', 10), [searchParams]);
  const sortBy = useMemo(() => searchParams?.get('sortBy') || 'createdAt', [searchParams]); // Default to createdAt
  const sortOrder = useMemo(() => (searchParams?.get('sortOrder') || 'desc') as 'asc' | 'desc', [searchParams]); // Default to desc for createdAt

  // Local state for the search input field for better UX (e.g., debounce or submit on enter)
  const [localSearchInput, setLocalSearchInput] = useState(searchTerm);

  // Ref for the element that triggers loading more items
  const loadMoreRef = useRef<HTMLDivElement | null>(null);

  // Effect to update local search input when URL searchTerm changes (e.g., back/forward navigation)
  useEffect(() => {
    setLocalSearchInput(searchTerm);
  }, [searchTerm]);

  const fetchFilterData = useCallback(async () => {
    setIsLoadingFilters(true);
    try {
      const [typesResult, categoriesResult, tagsResult] = await Promise.all([
        allEntityTypes.length === 0 ? getEntityTypes(session?.access_token) : Promise.resolve(allEntityTypes),
        allCategories.length === 0 ? getCategories(session?.access_token) : Promise.resolve(allCategories),
        allTags.length === 0 ? getTags(session?.access_token) : Promise.resolve(allTags),
      ]);

      if (allEntityTypes.length === 0 && Array.isArray(typesResult)) setAllEntityTypes(typesResult as EntityType[]);
      if (allCategories.length === 0 && Array.isArray(categoriesResult)) setAllCategories(categoriesResult as Category[]);
      if (allTags.length === 0 && Array.isArray(tagsResult)) setAllTags(tagsResult as Tag[]);
    } catch (err: any) {
      console.error('Failed to fetch filter data:', err);
      // Not setting main error state here, as entity fetching might still work
      // Or, you might want a specific error state for filters
    }
    setIsLoadingFilters(false);
  }, [session, allEntityTypes, allCategories, allTags]);

  const fetchEntitiesData = useCallback(async (isLoadMore = false) => {
    if (!isLoadMore) {
      setIsLoading(true);
      setEntities([]); // Clear entities for new filter/search, but not for load more
    } else {
      setIsLoadingMore(true);
    }
    setError(null);

    // Determine page to fetch
    const pageToFetch = isLoadMore && paginationMeta ? paginationMeta.page + 1 : currentPage;

    try {
      const params: GetEntitiesParams = {
        page: pageToFetch,
        limit: ITEMS_PER_PAGE,
        ...(searchTerm && { searchTerm }),
        ...(selectedEntityTypeIds.length > 0 && { entityTypeIds: selectedEntityTypeIds }),
        ...(selectedCategoryIds.length > 0 && { categoryIds: selectedCategoryIds }),
        ...(selectedTagIds.length > 0 && { tagIds: selectedTagIds }),
        ...(selectedFeatureIds.length > 0 && { featureIds: selectedFeatureIds }), // TODO: Add to API params when backend supports it
        sortBy: sortBy,
        sortOrder: sortOrder,
      };

      const entitiesResponse = await getEntities(params, session?.access_token);
      
      setEntities(prevEntities => 
        isLoadMore ? [...prevEntities, ...entitiesResponse.data] : entitiesResponse.data
      );
      setPaginationMeta(entitiesResponse.meta);

    } catch (err: any) {
      console.error('Failed to fetch entities:', err);
      setError(err.message || 'An unexpected error occurred while fetching data.');
      setEntities([]);
      setPaginationMeta(null);
    }
    if (!isLoadMore) {
      setIsLoading(false);
    } else {
      setIsLoadingMore(false);
    }
  }, [
    currentPage, // Still needed for initial load based on URL
    searchTerm,
    selectedEntityTypeIds,
    selectedCategoryIds,
    selectedTagIds,
    selectedFeatureIds, // Added
    sortBy,
    sortOrder,
    session,
    paginationMeta,
  ]);

  useEffect(() => {
    fetchFilterData();
  }, [fetchFilterData]);

  useEffect(() => {
    if (!isLoadingFilters) { // Fetch entities only after filter data attempts to load
        fetchEntitiesData();
    }
  }, [fetchEntitiesData, isLoadingFilters]);

  // Setup IntersectionObserver
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && paginationMeta?.hasNextPage && !isLoading && !isLoadingMore) {
          // console.log('Reached bottom, loading more...');
          fetchEntitiesData(true); // Pass true for loadMore
        }
      },
      { threshold: 1.0 } // Trigger when 100% of the target is visible
    );

    const currentLoadMoreRef = loadMoreRef.current;
    if (currentLoadMoreRef) {
      observer.observe(currentLoadMoreRef);
    }

    return () => {
      if (currentLoadMoreRef) {
        observer.unobserve(currentLoadMoreRef);
      }
    };
  }, [fetchEntitiesData, paginationMeta, isLoading, isLoadingMore]); // Add dependencies

  // Function to update URL query parameters
  const updateQueryAndNavigate = (newQueryValues: Record<string, string | string[] | null>) => {
    const current = new URLSearchParams(Array.from(searchParams?.entries() || []));
    let resetPage = false;

    Object.entries(newQueryValues).forEach(([key, value]) => {
      current.delete(key); // Always delete first to handle array values correctly
      if (value !== null && value !== '' && (!Array.isArray(value) || value.length > 0)) {
        if (Array.isArray(value)) {
          value.forEach(v => current.append(key, v));
        } else {
          current.set(key, value as string);
        }
      }
      // Reset page if any filter or search term changes, but not if only page itself changes
      if (key !== 'page' && !(key === 'sortBy' && newQueryValues['sortBy'] === sortBy) && !(key === 'sortOrder' && newQueryValues['sortOrder'] === sortOrder)) {
        resetPage = true;
      }
    });

    if (resetPage) {
      current.set('page', '1');
    }

    router.push(`${pathname}?${current.toString()}`, { scroll: false });
  };

  const handleSearchSubmit = (e?: React.FormEvent<HTMLFormElement>) => {
    e?.preventDefault();
    updateQueryAndNavigate({ searchTerm: localSearchInput });
  };

  const handleEntityTypeToggle = (typeId: string) => {
    const newSelectedIds = selectedEntityTypeIds.includes(typeId)
      ? selectedEntityTypeIds.filter(id => id !== typeId)
      : [...selectedEntityTypeIds, typeId];
    updateQueryAndNavigate({ entityTypeIds: newSelectedIds });
  };

  const handleCategoryToggle = (categoryId: string) => { // New handler
    const newSelectedIds = selectedCategoryIds.includes(categoryId)
      ? selectedCategoryIds.filter(id => id !== categoryId)
      : [...selectedCategoryIds, categoryId];
    updateQueryAndNavigate({ categoryIds: newSelectedIds });
  };

  const handleTagToggle = (tagId: string) => {
    const newSelectedIds = selectedTagIds.includes(tagId)
      ? selectedTagIds.filter(id => id !== tagId)
      : [...selectedTagIds, tagId];
    updateQueryAndNavigate({ tagIds: newSelectedIds });
  };

  const handleFeatureToggle = (featureId: string) => {
    const newSelectedIds = selectedFeatureIds.includes(featureId)
      ? selectedFeatureIds.filter(id => id !== featureId)
      : [...selectedFeatureIds, featureId];
    updateQueryAndNavigate({ featureIds: newSelectedIds });
  };

  const handleClearAllFilters = () => {
    setLocalSearchInput(''); // Also clear local search input
    updateQueryAndNavigate({
      searchTerm: null,
      entityTypeIds: null,
      categoryIds: null,
      tagIds: null,
      featureIds: null, // New
      page: '1'
    });
  };

  const handleSortByChange = (newSortBy: string) => {
    updateQueryAndNavigate({ sortBy: newSortBy });
  };

  const handleSortOrderToggle = () => {
    const newSortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
    updateQueryAndNavigate({ sortOrder: newSortOrder });
  };

  const handlePageClick = (newPage: number) => {
    updateQueryAndNavigate({ page: newPage.toString() });
  };

  const getPillName = (id: string, source: 'entityType' | 'category' | 'tag' | 'feature'): string => {
    if (source === 'entityType') return allEntityTypes.find(t => t.id === id)?.name || id;
    if (source === 'category') return allCategories.find(c => c.id === id)?.name || id;
    if (source === 'tag') return allTags.find(t => t.id === id)?.name || id;
    if (source === 'feature') return FEATURES_FILTER_OPTIONS.find(f => f.id === id)?.name || id;
    return id;
  };

  const activeFiltersForPills = useMemo(() => {
    return [
      ...selectedEntityTypeIds.map(id => ({ id, name: getPillName(id, 'entityType'), type: 'entityTypeIds' as const })),
      ...selectedCategoryIds.map(id => ({ id, name: getPillName(id, 'category'), type: 'categoryIds' as const })),
      ...selectedTagIds.map(id => ({ id, name: getPillName(id, 'tag'), type: 'tagIds' as const })),
      ...selectedFeatureIds.map(id => ({ id, name: getPillName(id, 'feature'), type: 'featureIds' as const })),
    ];
  }, [selectedEntityTypeIds, selectedCategoryIds, selectedTagIds, selectedFeatureIds, allEntityTypes, allCategories, allTags]);

  const handleRemovePill = (id: string, type: 'entityTypeIds' | 'categoryIds' | 'tagIds' | 'featureIds') => {
    if (type === 'entityTypeIds') handleEntityTypeToggle(id);
    if (type === 'categoryIds') handleCategoryToggle(id);
    if (type === 'tagIds') handleTagToggle(id);
    if (type === 'featureIds') handleFeatureToggle(id);
  };

  const renderFilterSection = (
    title: string,
    items: Array<{ id: string; name: string; count?: number }>,
    selectedIds: string[],
    toggleHandler: (id: string) => void,
    isLoading?: boolean,
    isExpanded?: boolean,
    onToggleExpand?: () => void,
    filterSearchTerm?: string,
    onFilterSearchChange?: (value: string) => void
  ) => {
    const displayLimit = 5;

    const filteredItems = useMemo(() => {
      if (!filterSearchTerm) return items;
      return items.filter(item => 
        item.name.toLowerCase().includes(filterSearchTerm.toLowerCase())
      );
    }, [items, filterSearchTerm]);

    const displayedItems = isExpanded ? filteredItems : filteredItems.slice(0, displayLimit);

    return (
      <div className="mb-6">
        <h3 className="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">{title}</h3>
        
        {onFilterSearchChange && items.length > displayLimit && (
          <div className="mb-2 relative">
            <Input
              type="text"
              placeholder={`Search ${title.toLowerCase()}...`}
              value={filterSearchTerm}
              onChange={(e) => onFilterSearchChange(e.target.value)}
              className="w-full text-xs h-8 pl-7 pr-2 py-1 rounded-md border-gray-300 dark:border-gray-600 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 shadow-sm"
            />
            <SearchIcon className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-400 dark:text-gray-500" />
          </div>
        )}

        {isLoading ? (
          <div className="space-y-2">
            {[1,2,3].map(i => <div key={i} className="h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>)}
          </div>
        ) : items.length > 0 ? (
          <ul className="space-y-2">
            {displayedItems.map((item) => (
              <li key={item.id} className="flex items-center justify-between">
                <Label htmlFor={`${title.toLowerCase().replace(' ', '-')}-${item.id}`} className="flex items-center text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-150 cursor-pointer text-sm">
                  <Checkbox
                    id={`${title.toLowerCase().replace(' ', '-')}-${item.id}`}
                    checked={selectedIds.includes(item.id)}
                    onCheckedChange={() => toggleHandler(item.id)}
                    className="mr-2 h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-600 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:data-[state=checked]:bg-primary-500"
                  />
                  {item.name}
                </Label>
                {/* Placeholder for count, styled as in image */}
                <span className="text-xs text-gray-400 dark:text-gray-500 bg-gray-200 dark:bg-gray-700 px-1.5 py-0.5 rounded-full">
                  {item.count || Math.floor(Math.random() * 300) + 10} 
                </span>
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {filterSearchTerm && filteredItems.length === 0 
              ? `No ${title.toLowerCase()} found for "${filterSearchTerm}".`
              : `No ${title.toLowerCase()} available.`}
          </p>
        )}
        {items.length > displayLimit && onToggleExpand && (
          <Button
            variant="link"
            onClick={onToggleExpand}
            className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 text-sm p-0 mt-2 h-auto min-h-[36px] flex items-center px-1"
          >
            {isExpanded ? 'Show less' : 'Show more'}
          </Button>
        )}
      </div>
    );
  };

  if (isLoading && entities.length === 0) { // Show loading only if no entities are yet displayed
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <p className="text-xl text-gray-600 dark:text-gray-300">Loading resources...</p>
        {/* You can add a spinner component here */}
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <p className="text-xl text-red-600 dark:text-red-400">Error: {error}</p>
        <Button onClick={() => { fetchFilterData(); fetchEntitiesData();}} className="mt-4" variant="destructive">
          Try Again
        </Button>
      </div>
    );
  }

  if (!isLoading && entities.length === 0 && !error) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <p className="text-xl text-gray-600 dark:text-gray-300">No resources found.</p>
        {/* You could add a link to a submission page or suggest refining filters */}
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-foreground mb-2">
            Discover AI Tools
          </h1>
          <p className="text-lg text-muted-foreground">
            Explore the latest AI tools, resources, and platforms to supercharge your workflow
          </p>
        </div>

        {/* Mobile Filter Toggle */}
        <div className="lg:hidden mb-6">
          <Button
            variant="outline"
            onClick={() => setIsMobileFilterOpen(!isMobileFilterOpen)}
            className="inline-flex items-center gap-2"
          >
            <SlidersHorizontalIcon className="h-4 w-4" />
            Filters
            {(selectedEntityTypeIds.length + selectedCategoryIds.length + selectedTagIds.length + selectedFeatureIds.length) > 0 && (
              <Badge variant="secondary" className="ml-1">
                {selectedEntityTypeIds.length + selectedCategoryIds.length + selectedTagIds.length + selectedFeatureIds.length}
              </Badge>
            )}
          </Button>
        </div>

        <div className="lg:flex lg:gap-8">
          {/* Filter Sidebar */}
          <aside
            className={[
              "w-full lg:w-80 space-y-6 mb-8 lg:mb-0",
              isMobileFilterOpen
                ? "fixed inset-0 z-50 bg-background p-6 overflow-y-auto lg:static lg:bg-transparent lg:p-0"
                : "hidden lg:block"
            ].join(' ')}
          >
            {/* Mobile Header */}
            {isMobileFilterOpen && (
              <div className="flex justify-between items-center pb-6 border-b border-border lg:hidden">
                <h2 className="text-xl font-semibold">Filters</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsMobileFilterOpen(false)}
                  className="h-8 w-8"
                >
                  <XIcon className="h-4 w-4" />
                </Button>
              </div>
            )}

            {/* Filter Sections */}
            <div className="space-y-6">
              <SmartFilterSection
                title="Resource Type"
                items={allEntityTypes.map(et => ({...et, count: Math.floor(Math.random() * 300) + 10}))}
                selectedIds={selectedEntityTypeIds}
                onToggle={handleEntityTypeToggle}
                isLoading={isLoadingFilters}
                defaultExpanded={true}
              />

              <SmartFilterSection
                title="Category"
                items={allCategories.map(c => ({...c, count: Math.floor(Math.random() * 150) + 5}))}
                selectedIds={selectedCategoryIds}
                onToggle={handleCategoryToggle}
                isLoading={isLoadingFilters}
              />

              <SmartFilterSection
                title="Features"
                items={FEATURES_FILTER_OPTIONS}
                selectedIds={selectedFeatureIds}
                onToggle={handleFeatureToggle}
                isLoading={false}
              />

              <SmartFilterSection
                title="Tags"
                items={allTags.map(t => ({...t, count: Math.floor(Math.random() * 50) + 1}))}
                selectedIds={selectedTagIds}
                onToggle={handleTagToggle}
                isLoading={isLoadingFilters}
              />
            </div>
          </aside>

          {/* Main Content Area */}
          <main className="flex-1 space-y-6">
            {/* Search Section */}
            <div className="bg-card border border-border rounded-lg p-6">
              <form onSubmit={handleSearchSubmit} className="space-y-4">
                <div className="relative">
                  <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                  <Input
                    id="search-input"
                    type="search"
                    value={localSearchInput}
                    onChange={(e) => setLocalSearchInput(e.target.value)}
                    placeholder="Search AI tools, platforms, and resources..."
                    className="pl-10 h-12 text-base"
                  />
                </div>

                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                  <div className="text-sm text-muted-foreground">
                    {paginationMeta && paginationMeta.totalItems ? (
                      <span>
                        <span className="font-medium text-foreground">{paginationMeta.totalItems}</span> resources found
                      </span>
                    ) : (
                      'Loading...'
                    )}
                  </div>

                  <Select value={sortBy} onValueChange={handleSortByChange}>
                    <SelectTrigger className="w-full sm:w-auto min-w-[200px]">
                      <SelectValue placeholder="Sort by..." />
                    </SelectTrigger>
                    <SelectContent>
                      {SORT_OPTIONS.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </form>
            </div>

            {/* Active Filter Pills */}
            <ActiveFilterPills
              filters={activeFiltersForPills}
              onRemove={handleRemovePill}
              onClearAll={handleClearAllFilters}
              searchTerm={searchTerm}
              onClearSearch={() => {
                setLocalSearchInput('');
                updateQueryAndNavigate({ searchTerm: null });
              }}
            />


            {/* Content Display Area */}
            {isLoading && entities.length === 0 && ( // Show loading skeletons only if no entities are yet displayed
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {[...Array(ITEMS_PER_PAGE)].map((_, index) => (
                  <ResourceCardSkeleton key={`skeleton-${index}`} />
                ))}
              </div>
            )}

            {!isLoading && error && (
              <div className="text-center py-10 bg-red-50 dark:bg-red-900/30 p-6 rounded-lg shadow">
                <p className="text-lg text-red-600 dark:text-red-400 mb-3">Error: {error}</p>
                <Button onClick={() => { fetchFilterData(); fetchEntitiesData();}} variant="destructive">
                  Try Again
                </Button>
              </div>
            )}

            {!isLoading && !error && entities.length === 0 && (
              <div className="text-center py-16">
                <div className="max-w-md mx-auto">
                  <div className="w-16 h-16 mx-auto mb-4 bg-muted rounded-full flex items-center justify-center">
                    <SearchIcon className="w-8 h-8 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium text-foreground mb-2">
                    No resources found
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    We couldn't find any resources matching your criteria.
                  </p>
                  <Button
                    variant="outline"
                    onClick={handleClearAllFilters}
                    className="text-sm"
                  >
                    Clear all filters
                  </Button>
                </div>
              </div>
            )}

            {!isLoading && !error && entities.length > 0 && (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                  {entities.map((entity) => (
                    <ResourceCard
                      key={entity.id}
                      entity={entity}
                      onBookmark={(entityId) => {
                        // TODO: Implement bookmark functionality
                        console.log('Bookmark entity:', entityId);
                      }}
                      onShare={(entity) => {
                        // TODO: Implement share functionality
                        console.log('Share entity:', entity);
                      }}
                    />
                  ))}
                </div>

                {/* Load More Trigger / Indicator */}
                {paginationMeta?.hasNextPage && (
                  <div ref={loadMoreRef} className="text-center py-10">
                    {isLoadingMore ? (
                      <p className="text-lg text-gray-500 dark:text-gray-400">Loading more resources...</p>
                      // Optionally add a spinner here
                    ) : (
                      // You can have a button here if you prefer a manual "Load More" action initially
                      // <Button onClick={() => fetchEntitiesData(true)}>Load More</Button>
                      <p className="text-sm text-gray-400 dark:text-gray-500">Scroll down to load more.</p>
                    )}
                  </div>
                )}
              </>
            )}
          </main>
        </div>
      </div>
      {/* Overlay for mobile when filter is open */}
      {isMobileFilterOpen && (
        <div 
          className="fixed inset-0 z-30 bg-black/30 lg:hidden"
          onClick={() => setIsMobileFilterOpen(false)}
        ></div>
      )}
    </div>
  );
} 